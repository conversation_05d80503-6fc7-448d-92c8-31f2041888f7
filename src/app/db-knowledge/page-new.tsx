"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import QueryInput from "@/components/query/QueryInput";
import { GraphsRow } from "@/components/graphs";
import FigmaTableDemo from "@/components/table";
import <PERSON><PERSON> from "lottie-react";
import robotAnimation2 from "../../../public/robot-lottie3.json";
import { useUIStore } from "@/store/uiStore";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { useTheme } from "@/contexts/ThemeContext";

// Import shadcn components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { 
  AlertCircle, 
  Database, 
  MessageSquare, 
  Search, 
  BarChart3, 
  Table, 
  Sparkles,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  TrendingUp,
  FileText,
  Zap,
  Activity,
  Users,
  DollarSign
} from "lucide-react";

// Import custom hooks
import {
  useBusinessRulesModal,
  useDatabaseOperations,
  useUserSettings
} from "@/lib/hooks";

const quickActions = [
  { 
    icon: BarChart3, 
    text: "Show me attendance for January 2024", 
    category: "Analytics",
    color: "bg-blue-500/10 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800",
    description: "View employee attendance records",
    gradient: "from-blue-500/20 to-blue-600/20"
  },
  { 
    icon: DollarSign, 
    text: "Show me the salary list for January 2024", 
    category: "Finance",
    color: "bg-green-500/10 text-green-600 dark:text-green-400 border-green-200 dark:border-green-800",
    description: "Access payroll information",
    gradient: "from-green-500/20 to-green-600/20"
  },
  { 
    icon: Clock, 
    text: "Show me the pending task for manager", 
    category: "Tasks",
    color: "bg-orange-500/10 text-orange-600 dark:text-orange-400 border-orange-200 dark:border-orange-800",
    description: "Review outstanding assignments",
    gradient: "from-orange-500/20 to-orange-600/20"
  },
  { 
    icon: TrendingUp, 
    text: "Show me the profit items", 
    category: "Reports",
    color: "bg-purple-500/10 text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-800",
    description: "Analyze revenue data",
    gradient: "from-purple-500/20 to-purple-600/20"
  },
];

export default function DBKnowledgePage() {
  const [query, setQuery] = useState("");
  const { showBusinessRulesModal, setShowBusinessRulesModal } = useUIStore();
  const { resolvedTheme } = useTheme();

  // Custom hooks
  const databaseOps = useDatabaseOperations();
  const businessRulesModal = useBusinessRulesModal();
  const userSettings = useUserSettings();

  const handleQuerySubmit = async (queryText: string) => {
    if (!queryText.trim()) return;
    await databaseOps.executeQuery(queryText);
  };

  // Enhanced data analysis for better visualization
  function analyzeDataForCharts(data: any[], keys: string[]) {
    if (!data || data.length === 0) return {};
    
    const numericKeys = keys.filter(key => 
      data.some(item => typeof item[key] === 'number' && !isNaN(item[key]))
    );
    
    const stringKeys = keys.filter(key => 
      data.some(item => typeof item[key] === 'string')
    );

    let chartData: any = {};

    // Generate pie chart for categorical data
    if (stringKeys.length > 0) {
      const key = stringKeys[0];
      const counts = data.reduce((acc, item) => {
        const value = item[key];
        acc[value] = (acc[value] || 0) + 1;
        return acc;
      }, {});
      
      chartData.pie = Object.entries(counts).map(([name, value]) => ({
        name,
        value,
        fill: `hsl(${Math.random() * 360}, 70%, 50%)`
      }));
    }

    // Generate bar chart for numeric data
    if (numericKeys.length > 0 && data.length <= 20) {
      const key = numericKeys[0];
      chartData.bar = data.map((item, index) => ({
        name: item[stringKeys[0]] || `Item ${index + 1}`,
        value: item[key] || 0
      }));
    }

    return chartData;
  }

  function renderDbData(dataArr: any) {
    if (!Array.isArray(dataArr)) return null;
    
    if (dataArr.length === 0) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="mt-6 border-dashed">
            <CardContent className="flex flex-col items-center justify-center py-16">
              <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
                <Database className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No Results Found</h3>
              <p className="text-muted-foreground text-center max-w-md">
                Your query didn't return any data. Try adjusting your search terms or check the database connection.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      );
    }
    
    if (dataArr.length === 1) {
      const obj = dataArr[0];
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="mt-6 max-w-4xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 flex items-center justify-center rounded-xl bg-primary/10 text-primary">
                  <CheckCircle className="w-6 h-6" />
                </div>
                <div>
                  <CardTitle className="text-xl">Single Record Found</CardTitle>
                  <CardDescription>Detailed information retrieved from database</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(obj).map(([key, value], index) => (
                  <motion.div
                    key={key}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="group p-4 rounded-lg border bg-card hover:shadow-md transition-all duration-200"
                  >
                    <Badge variant="secondary" className="mb-2 text-xs font-medium">
                      {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                    <p className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                      {String(value)}
                    </p>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      );
    }

    // Multiple records - show table and charts
    const allKeys = Array.from(new Set(dataArr.flatMap(Object.keys)));
    const currentData = dataArr;
    const chartData = analyzeDataForCharts(currentData, allKeys);
    
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mt-6 space-y-6"
      >
        <Card className="overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 flex items-center justify-center rounded-lg bg-primary/10 text-primary">
                  <Table className="w-5 h-5" />
                </div>
                <div>
                  <CardTitle className="flex items-center gap-2">
                    Query Results
                    <Badge variant="secondary" className="ml-2">
                      {currentData.length} records
                    </Badge>
                  </CardTitle>
                  <CardDescription>
                    Data retrieved from database query
                  </CardDescription>
                </div>
              </div>
              <Button variant="outline" size="sm" className="gap-2">
                <FileText className="w-4 h-4" />
                Export
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <FigmaTableDemo
              columns={allKeys.map((key) => ({ key, label: key }))}
              allKeys={allKeys}
              data={currentData}
              pageSizeOptions={[7, 10, 20, 30]}
              defaultPageSize={7}
            />
          </CardContent>
        </Card>
        
        {(chartData.pie || chartData.bar) && (
          <Card className="overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-secondary/5 to-secondary/10">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 flex items-center justify-center rounded-lg bg-secondary/10 text-secondary-foreground">
                  <BarChart3 className="w-5 h-5" />
                </div>
                <div>
                  <CardTitle>Data Visualization</CardTitle>
                  <CardDescription>
                    Interactive charts generated from your query results
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <GraphsRow
                chartData={chartData}
                loading={databaseOps.loading}
                isDummy={!chartData || (!chartData.bar && !chartData.pie)}
              />
            </CardContent>
          </Card>
        )}
      </motion.div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative overflow-hidden"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5" />
        <div className="relative">
          <Card className="border-0 shadow-none bg-transparent">
            <CardContent className="pt-8 pb-6">
              <div className="text-center mb-8">
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center shadow-lg"
                >
                  <Database className="w-8 h-8 text-primary-foreground" />
                </motion.div>
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent mb-2"
                >
                  Database Knowledge Hub
                </motion.h1>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="text-muted-foreground max-w-2xl mx-auto"
                >
                  Query your database using natural language. Get instant insights, visualizations, and detailed analysis.
                </motion.p>
              </div>

              <QueryInput
                query={query}
                setQuery={setQuery}
                handleQuerySubmit={handleQuerySubmit}
                loading={databaseOps.loading}
                selected={"db"}
                quickActions={quickActions}
                theme={resolvedTheme || "light"}
              />
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Results Section */}
      <div className="px-4 pb-8">
        <AnimatePresence mode="wait">
          {databaseOps.error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="mt-6 border-destructive/50 bg-destructive/5">
                <CardContent className="flex items-center gap-3 pt-6">
                  <XCircle className="h-5 w-5 text-destructive flex-shrink-0" />
                  <div>
                    <p className="text-destructive font-medium">Query Error</p>
                    <p className="text-destructive/80 text-sm">{databaseOps.error}</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
          
          {databaseOps.loading && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="mt-6 overflow-hidden">
                <CardContent className="flex flex-col items-center justify-center py-16 gap-6">
                  <div className="relative">
                    <div className="w-64 h-64">
                      <Lottie
                        animationData={robotAnimation2}
                        loop={true}
                        autoplay={true}
                      />
                    </div>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center"
                    >
                      <Loader2 className="w-4 h-4 text-primary" />
                    </motion.div>
                  </div>
                  <div className="text-center space-y-2">
                    <h3 className="text-xl font-semibold flex items-center gap-2 justify-center">
                      <Sparkles className="w-5 h-5 text-primary" />
                      Analyzing Your Query
                    </h3>
                    <p className="text-muted-foreground max-w-md">
                      Our AI is processing your request and searching through the database for relevant information.
                    </p>
                  </div>
                  <div className="w-full max-w-md space-y-3">
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Render Results */}
        {databaseOps.dbResponse &&
          databaseOps.dbResponse.payload &&
          renderDbData(databaseOps.dbResponse.payload.data)}
      </div>

      {/* Business Rules Modal */}
      <Dialog open={showBusinessRulesModal} onOpenChange={setShowBusinessRulesModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Business Rules & Constraints
            </DialogTitle>
            <DialogDescription>
              Database business rules, constraints, and operational guidelines
            </DialogDescription>
          </DialogHeader>
          
          <Separator />
          
          <div className="flex-1 overflow-auto">
            {businessRulesModal.businessRulesLoading ? (
              <div className="flex flex-col items-center justify-center py-12 space-y-4">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full"
                />
                <p className="text-muted-foreground">Loading business rules...</p>
                <div className="w-full space-y-3">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            ) : businessRulesModal.businessRulesError ? (
              <Card className="border-destructive/50 bg-destructive/5">
                <CardContent className="flex items-center gap-3 pt-6">
                  <AlertCircle className="h-5 w-5 text-destructive" />
                  <div>
                    <p className="text-destructive font-medium">Failed to Load Business Rules</p>
                    <p className="text-destructive/80 text-sm">{businessRulesModal.businessRulesError}</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {businessRulesModal.businessRulesText}
                    </ReactMarkdown>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
