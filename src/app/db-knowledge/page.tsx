"use client";
import { useState, useEffect } from "react";
import QueryInput from "@/components/query/QueryInput";
import { GraphsRow } from "@/components/graphs";
import FigmaTableDemo from "@/components/table";
import Lottie from "lottie-react";
import robotAnimation2 from "../../../public/robot-lottie3.json";
import { useUIStore } from "@/store/uiStore";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { useTheme } from "@/contexts/ThemeContext";

// Import shadcn components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AlertCircle, Database, MessageSquare } from "lucide-react";

// Import custom hooks
import {
  useBusinessRulesModal,
  useDatabaseOperations,
  useUserSettings
} from "@/lib/hooks";

const quickActions = [
  { icon: "�", text: "Show me attendance for January 2024", category: "Analytics" },
  { icon: "💰", text: "Show me the salary list for January 2024", category: "Finance" },
  { icon: "📋", text: "Show me the pending task for manager", category: "Tasks" },
  { icon: "�", text: "Show me the profit items", category: "Reports" },
];

export default function DBKnowledgePage() {
  const [query, setQuery] = useState("");
  const { showBusinessRulesModal, setShowBusinessRulesModal } = useUIStore();
  const { resolvedTheme } = useTheme();
  
  // Custom hooks
  const businessRulesModal = useBusinessRulesModal();
  const databaseOps = useDatabaseOperations();
  const userSettings = useUserSettings();

  // Initialize component
  useEffect(() => {
    databaseOps.fetchQueryHistory(userSettings.userId);
  }, [userSettings.userId]);

  // Handle business rules modal
  useEffect(() => {
    if (showBusinessRulesModal) {
      businessRulesModal.openModal();
    }
  }, [showBusinessRulesModal]);

  const handleQuerySubmit = async () => {
    if (!query.trim()) return;
    
    try {
      await databaseOps.sendDatabaseQuery(query, userSettings.userId);
    } catch (e) {
      // Error handling is done in the hook
    }
    setQuery("");
  };

  function renderDbData(dataArr: any) {
    if (!Array.isArray(dataArr)) return null;
    if (dataArr.length === 0) {
      return (
        <Card className="mt-6">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Database className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground text-center">No data found for your query.</p>
          </CardContent>
        </Card>
      );
    }

    if (dataArr.length === 1) {
      const obj = dataArr[0];
      return (
        <Card className="mt-6 max-w-4xl">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 flex items-center justify-center rounded-lg bg-primary/10 text-primary">
                <MessageSquare className="w-5 h-5" />
              </div>
              <div>
                <CardTitle className="text-lg">Single Record Found</CardTitle>
                <CardDescription>Detailed information below</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(obj).map(([key, value]) => (
                <div
                  key={key}
                  className="flex flex-col gap-2 p-4 rounded-lg border bg-card"
                >
                  <Badge variant="secondary" className="w-fit text-xs">
                    {key}
                  </Badge>
                  <span className="text-sm font-medium">
                    {String(value)}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      );
    }
    // Multiple objects: show as list/table with pagination
    const allKeys = Array.from(
      new Set(dataArr.flatMap((obj: any) => Object.keys(obj)))
    );
    const currentData = dataArr;
    // --- Graph logic ---
    const analyzeDataForCharts = (data: any[], keys: string[]) => {
      const chartData: {
        pie?: { key: string; data: any[] };
        bar?: { key: string; data: any[] };
        line?: { key: string; data: any[] };
      } = {};
      for (const key of keys) {
        const values = data
          .map((obj: any) => obj[key])
          .filter((v) => v !== null && v !== undefined);
        if (values.length === 0) continue;
        const unique = Array.from(new Set(values));
        const isNumeric = values.every(
          (v) => !isNaN(Number(v)) && typeof v !== "boolean"
        );
        const isDate = values.every((v) => !isNaN(Date.parse(v)));
        const uniqueCount = unique.length;
        if (
          !chartData.pie &&
          uniqueCount >= 2 &&
          uniqueCount <= 8 &&
          !isNumeric
        ) {
          const counts: Record<string, number> = {};
          data.forEach((obj: any) => {
            const val = String(obj[key] || "Unknown");
            counts[val] = (counts[val] || 0) + 1;
          });
          chartData.pie = {
            key,
            data: Object.entries(counts)
              .sort(([, a], [, b]) => Number(b) - Number(a))
              .map(([name, value]) => ({ name, value })),
          };
        }
        if (!chartData.bar && isNumeric && uniqueCount > 1) {
          const groupKey = keys[0];
          const grouped = new Map<string, number>();
          data.forEach((obj: any) => {
            const groupVal = obj[groupKey];
            const metricVal = Number(obj[key]) || 0;
            grouped.set(groupVal, (grouped.get(groupVal) || 0) + metricVal);
          });
          const sortedData = Array.from(grouped.entries())
            .map(([name, value]) => ({ name, [key]: value }))
            .sort((a, b) => Number(b[key]) - Number(a[key]))
            .slice(0, 10);
          if (sortedData.length > 0) {
            chartData.bar = { key, data: sortedData };
          }
        }
        if (!chartData.line && (isDate || (isNumeric && uniqueCount > 5))) {
          let lineData;
          if (isDate) {
            const dateCounts: Record<string, number> = {};
            data.forEach((obj: any) => {
              const date = new Date(obj[key]).toLocaleDateString();
              dateCounts[date] = (dateCounts[date] || 0) + 1;
            });
            lineData = Object.entries(dateCounts)
              .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
              .map(([name, value]) => ({ name, value }));
          } else {
            lineData = data.slice(0, 20).map((obj: any, idx) => ({
              name: obj[keys[0]] || `Item ${idx + 1}`,
              value: Number(obj[key]),
            }));
          }
          if (lineData.length > 1) {
            chartData.line = { key, data: lineData };
          }
        }
      }
      return chartData;
    };
    const chartData = analyzeDataForCharts(currentData, allKeys);
    return (
      <div className="mt-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Query Results
            </CardTitle>
            <CardDescription>
              Found {currentData.length} record{currentData.length !== 1 ? 's' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FigmaTableDemo
              columns={allKeys.map((key) => ({ key, label: key }))}
              allKeys={allKeys}
              data={currentData}
              pageSizeOptions={[7, 10, 20, 30]}
              defaultPageSize={7}
            />
          </CardContent>
        </Card>

        {(chartData.pie || chartData.bar || chartData.line) && (
          <Card>
            <CardHeader>
              <CardTitle>Data Visualization</CardTitle>
              <CardDescription>
                Interactive charts generated from your query results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="charts-section">
                <GraphsRow
                  chartData={chartData}
                  loading={databaseOps.loading}
                  isDummy={
                    !chartData ||
                    (!chartData.line && !chartData.bar && !chartData.pie)
                  }
                />
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <Card>
        <CardContent className="pt-6">
          <QueryInput
            query={query}
            setQuery={setQuery}
            handleQuerySubmit={handleQuerySubmit}
            loading={databaseOps.loading}
            selected={"db"}
            quickActions={quickActions}
            theme={resolvedTheme || "light"}
          />
        </CardContent>
      </Card>
      {databaseOps.error && (
        <Card className="mt-6 border-destructive/50">
          <CardContent className="flex items-center gap-3 pt-6">
            <AlertCircle className="h-5 w-5 text-destructive" />
            <p className="text-destructive">{databaseOps.error}</p>
          </CardContent>
        </Card>
      )}

      {databaseOps.loading && (
        <Card className="mt-6">
          <CardContent className="flex flex-col items-center justify-center py-12 gap-6">
            <div className="w-64 h-64">
              <Lottie
                animationData={robotAnimation2}
                loop={true}
                autoplay={true}
              />
            </div>
            <div className="text-center space-y-2">
              <h3 className="text-xl font-semibold">
                Searching for answers...
              </h3>
              <p className="text-muted-foreground">
                Our AI is analyzing your query and searching through the database
              </p>
            </div>
            <div className="w-full max-w-md space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
      )}
      {!databaseOps.loading &&
        databaseOps.dbResponse &&
        databaseOps.dbResponse.payload &&
        renderDbData(databaseOps.dbResponse.payload.data)}
      {/* Business Rules Modal using shadcn Dialog */}
      <Dialog open={showBusinessRulesModal} onOpenChange={setShowBusinessRulesModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Business Rules
            </DialogTitle>
            <DialogDescription>
              Database business rules and constraints
            </DialogDescription>
          </DialogHeader>

          <Separator />

          <div className="flex-1 overflow-auto">
            {businessRulesModal.businessRulesLoading ? (
              <div className="flex flex-col items-center justify-center py-12 space-y-4">
                <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
                <p className="text-muted-foreground">Loading business rules...</p>
                <div className="w-full space-y-3">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            ) : businessRulesModal.businessRulesError ? (
              <Card className="border-destructive/50">
                <CardContent className="flex items-center gap-3 pt-6">
                  <AlertCircle className="h-5 w-5 text-destructive" />
                  <p className="text-destructive font-medium">
                    {businessRulesModal.businessRulesError}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <div className="markdown-body prose prose-sm max-w-none dark:prose-invert">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {businessRulesModal.businessRulesText}
                    </ReactMarkdown>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
