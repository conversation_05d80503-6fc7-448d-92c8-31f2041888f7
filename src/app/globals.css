@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-gradient-to-br from-[#f0f9f5] to-[#ffffff] dark:from-[#001a13] dark:to-[#002a1f];
  }
}

/* ZenUI-style Enhanced Dark Mode Transitions */
@supports (view-transition-name: none) {
  /* Disable default view transition animations */
  ::view-transition-old(root),
  ::view-transition-new(root) {
    animation: none;
    mix-blend-mode: normal;
  }

  ::view-transition-old(root) {
    z-index: 1;
  }

  ::view-transition-new(root) {
    z-index: 2;
  }

  /* Ensure smooth blending between themes */
  ::view-transition-group(root) {
    animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* Prevent any flickering during transition */
  html {
    view-transition-name: root;
  }
}

/* Smooth transitions for all theme-aware elements */
* {
  transition:
    background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    border-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    fill 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    stroke 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Prevent transition on page load */
.no-transition * {
  transition: none !important;
}

/* ZenUI-style enhanced theme toggle animations */
.theme-toggle-button {
  position: relative;
  overflow: hidden;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced smooth icon transitions */
.theme-toggle-button svg,
.theme-toggle-button [data-lucide] {
  transition:
    transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    scale 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
  will-change: transform, opacity, scale;
}

/* Prevent any jank during transitions */
.theme-toggle-button svg * {
  transition:
    fill 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    stroke 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Smooth icon state transitions with staggered timing */
.theme-toggle-button .lucide-sun {
  transition-delay: 0ms;
}

.theme-toggle-button .lucide-moon {
  transition-delay: 50ms;
}

/* Enhanced hover effects for smoother interaction */
.theme-toggle-button:hover svg {
  transform: scale(1.05);
}

.theme-toggle-button:active svg {
  transform: scale(0.95);
  transition-duration: 0.1s;
}

/* ZenUI-style contracting circle effect for light to dark */
.theme-toggle-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.7) 40%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none;
  opacity: 0;
}

/* ZenUI-style expanding circle effect for dark to light */
.theme-toggle-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 40%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none;
  opacity: 0;
}

/* Active states for ZenUI-style transitions */
.theme-toggle-button.switching-to-dark::before {
  transform: translate(-50%, -50%) scale(1.2);
  opacity: 1;
}

.theme-toggle-button.switching-to-light::after {
  transform: translate(-50%, -50%) scale(1.2);
  opacity: 1;
}

/* Subtle button scale effect */
.theme-toggle-button:active {
  transform: scale(0.98);
}

/* Enhanced focus states */
.theme-toggle-button:focus-visible {
  outline: 2px solid rgb(34, 197, 94);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.1);
}

/* Light mode variables */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 160 84% 39%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 160 84% 39%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --sidebar: 0 0% 100%;
  --sidebar-foreground: 222.2 84% 4.9%;
  --sidebar-primary: 160 84% 39%;
  --sidebar-primary-foreground: 210 40% 98%;
  --sidebar-accent: 210 40% 96%;
  --sidebar-accent-foreground: 222.2 84% 4.9%;
  --sidebar-border: 214.3 31.8% 91.4%;
  --sidebar-ring: 160 84% 39%;
}

/* ====== scrollbar ========= */
.tb-parent {
  --sb-track-color: #d8dadbda;
  --sb-thumb-color: #9fa1a086;
  --sb-size: 20px;
}

.tb-parent::-webkit-scrollbar {
  width: var(--sb-size); /* width of the entire scrollbar */
}

.tb-parent::-webkit-scrollbar-track {
  background: var(--sb-track-color);
  border-radius: 9px; /* roundness of the scroll thumb */
}

.tb-parent::-webkit-scrollbar-thumb {
  background: var(--sb-thumb-color);
  border-radius: 9px;
}

/* Prevent initial flash */
html {
  color-scheme: light dark;
}

body {
  @apply antialiased;
}

/* Smooth transitions */
* {
  @apply transition-colors duration-200;
}

/* Custom scrollbar for both light and dark mode */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.18);
}

.dark ::-webkit-scrollbar-track {
  background: rgba(0, 255, 185, 0.05);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 185, 0.1);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 185, 0.15);
}

/* Table parent container */
.tb-parent {
  @apply bg-white/10 dark:bg-[#011f17]/40;
  backdrop-filter: blur(8px);
}

/* Card styles */
.card-gradient {
  @apply bg-gradient-to-br from-white/80 to-gray-50/40 dark:from-[#011f17]/80 dark:to-[#011f17]/40;
  backdrop-filter: blur(10px);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .card-gradient {
  box-shadow: 0 0 20px rgba(0, 255, 185, 0.03);
  border: 1px solid rgba(54, 62, 60, 0.1);
}

/* Button styles */
.btn-primary {
  @apply bg-gradient-to-r from-[#5BE49B] to-[#00A76F] text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-all transform hover:-translate-y-0.5;
}
.btn-primary:disabled {
  @apply from-gray-400 to-gray-500 cursor-not-allowed;
}

.btn-secondary {
  @apply bg-[#f0f9f5] hover:bg-[#e1f4ea] dark:bg-[#011f17] dark:hover:bg-[#012920] text-[#00bf6f] dark:text-[#00bf6f];
}

.btn-outline {
  @apply border border-gray-200 dark:border-[#012920] bg-white/50 dark:bg-[#011f17]/50 hover:bg-gray-50 dark:hover:bg-[#012920] text-gray-700 dark:text-[#00bf6f];
}

/* Custom input styles */
.input-gradient {
  @apply bg-gradient-to-r from-gray-100/90 to-gray-50/80 dark:from-[#011f17]/90 dark:to-[#012920]/80;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
}

.input-gradient::placeholder {
  @apply text-gray-500 dark:text-gray-400;
}

.input-gradient:focus {
  @apply bg-gradient-to-r from-white/95 to-gray-50/90 dark:from-[#012920]/95 dark:to-[#011f17]/90;
  border-color: rgba(0, 191, 111, 0.3);
  box-shadow: 0 0 0 1px rgba(0, 191, 111, 0.1), 
              inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
}

/* Custom styles for the onboarding tour */
.react-joyride__overlay {
  backdrop-filter: blur(2px);
}

.react-joyride__spotlight {
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
}

/* Ensure tour elements are visible */
.sidebar-collections,
.db-knowledge-btn,
.submit-btn,
.filter-toggle,
.pagination-controls,
.charts-section,
.user-id-section,
.query-history,
.data-table {
  position: relative;
  z-index: 1;
}

/* Dark mode adjustments for tour */
.dark .react-joyride__tooltip {
  background-color: #011f17 !important;
  border-color: #013828 !important;
  color: #e5e7eb !important;
}

.dark .react-joyride__tooltip__title {
  color: #00bf6f !important;
}

.dark .react-joyride__tooltip__content {
  color: #9ca3af !important;
}

.dark .react-joyride__button--back {
  background-color: #012920 !important;
  border-color: #013828 !important;
  color: #e5e7eb !important;
}

.dark .react-joyride__button--skip,
.dark .react-joyride__button--close {
  color: #9ca3af !important;
}

@keyframes zoom-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.react-joyride__tooltip {
  animation: zoom-in 0.3s ease-in-out;
}

/* ====== Input Section Animations ========= */
@keyframes pulse-subtle {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.01);
    opacity: 0.98;
  }
}

@keyframes input-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(0, 191, 111, 0.1);
  }
  50% {
    box-shadow: 0 0 20px 0 rgba(0, 191, 111, 0.3);
  }
}

@keyframes input-pulse {
  0%, 100% {
    border-color: rgba(225, 244, 234, 0.3);
    background: rgba(255, 255, 255, 0.5);
  }
  50% {
    border-color: rgba(0, 191, 111, 0.4);
    background: rgba(255, 255, 255, 0.7);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes button-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 191, 111, 0.2);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 191, 111, 0.4);
  }
}

/* Animation classes */
.animate-pulse-subtle {
  animation: pulse-subtle 4s ease-in-out infinite;
}

.animate-input-glow {
  animation: input-glow 3s ease-in-out infinite;
}

.animate-input-pulse {
  animation: input-pulse 4s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 3s ease-in-out infinite;
}

.animate-button-pulse {
  animation: button-pulse 2s ease-in-out infinite;
}

/* Dark mode adjustments for animations */
.dark .animate-input-pulse {
  animation: input-pulse-dark 4s ease-in-out infinite;
}

@keyframes input-pulse-dark {
  0%, 100% {
    border-color: rgba(1, 56, 40, 0.3);
    background: rgba(1, 31, 23, 0.5);
  }
  50% {
    border-color: rgba(0, 191, 111, 0.4);
    background: rgba(1, 31, 23, 0.7);
  }
}

.dark .animate-input-glow {
  animation: input-glow-dark 3s ease-in-out infinite;
}

@keyframes input-glow-dark {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(0, 191, 111, 0.05);
  }
  50% {
    box-shadow: 0 0 20px 0 rgba(0, 191, 111, 0.2);
  }
}

.markdown-body h1 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-top: 1.2em;
  margin-bottom: 0.5em;
}
.markdown-body h2 {
  font-size: 1.25rem;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}
.markdown-body h3 {
  font-size: 1.1rem;
  font-weight: bold;
  margin-top: 0.8em;
  margin-bottom: 0.4em;
}
.markdown-body ul, .markdown-body ol {
  margin-left: 1.5em;
  margin-bottom: 1em;
}
.markdown-body li {
  margin-bottom: 0.3em;
}
.markdown-body code, .markdown-body pre {
  background: #f3f3f3;
  color: #333;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;
}
.markdown-body {
  color: #222;
}
.dark .markdown-body {
  color: #e5e7eb;
}

/* Quick Action Card Glassmorphic Gradient Border */
.quick-action-card {
  position: relative;

  overflow: hidden;
  border-radius: 20px;
  border: 2px solid var(--Glass-strock, rgba(221, 255, 237, 0.27));
  background:
    linear-gradient(180deg, rgba(140, 252, 207, 0.423) 0%, rgba(148, 255, 212, 0.115) 52.11%, rgba(255, 255, 255, 0) 100%);
  backdrop-filter: blur(50px);
  color: #B2D9C7;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  height: 120px;
  padding: 20px;
  gap: 10px;
  transition: all 0.3s ease;
}

.quick-action-card > * {
  position: relative;
  z-index: 1;
}

/* Animated card entrance */
.animated-card {
  animation: cardEntrance 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes cardEntrance {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Floating particles */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.floating-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(91, 228, 155, 0.6);
  border-radius: 50%;
  animation: float 4s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.8;
  }
  75% {
    opacity: 1;
  }
}

/* Glowing border effect */
.glow-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 16px;
  background: linear-gradient(45deg, 
    rgba(91, 228, 155, 0.3), 
    rgba(91, 228, 155, 0.1), 
    rgba(91, 228, 155, 0.3), 
    rgba(91, 228, 155, 0.1)
  );
  background-size: 400% 400%;
  animation: glowPulse 3s ease-in-out infinite;
  z-index: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

@keyframes glowPulse {
  0%, 100% {
    background-position: 0% 50%;
    opacity: 0;
  }
  50% {
    background-position: 100% 50%;
    opacity: 1;
  }
}

/* Card icon animation */
.card-icon {
  animation: iconFloat 3s ease-in-out infinite;
  transition: transform 0.3s ease;
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* Card text animation */
.card-text {
  animation: textGlow 4s ease-in-out infinite;
  transition: all 0.3s ease;
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(91, 228, 155, 0.3);
  }
  50% {
    text-shadow: 0 0 15px rgba(91, 228, 155, 0.6);
  }
}

/* Hover effects */
.quick-action-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 30px rgba(91, 228, 155, 0.2);
  border-color: rgba(91, 228, 155, 0.5);
}

.quick-action-card:hover .glow-border {
  opacity: 1;
}

.quick-action-card:hover .card-icon {
  transform: scale(1.1) translateY(-3px);
}

.quick-action-card:hover .card-text {
  color: #5BE49B;
  text-shadow: 0 0 20px rgba(91, 228, 155, 0.8);
}

/* Continuous background animation */
.quick-action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(91, 228, 155, 0.1),
    transparent
  );
  animation: shimmerEffect 4s ease-in-out infinite;
  z-index: 0;
}

@keyframes shimmerEffect {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Custom utility classes for light/dark mode */
.backdrop-blur-12 {
  backdrop-filter: blur(12px);
}

.backdrop-blur-22 {
  backdrop-filter: blur(22.5px);
}

.backdrop-blur-32 {
  backdrop-filter: blur(32px);
}

.w-70 {
  width: 280px;
}

.w-863 {
  width: 863px;
}

.top-15 {
  top: 60px;
}

.max-w-95vw {
  max-width: 95vw;
}

.max-h-95vh {
  max-height: 95vh;
}

.min-h-300 {
  min-height: 300px;
}

.gap-4\.5 {
  gap: 18px;
}

.border-3 {
  border-width: 3px;
}

/* Markdown body styles for light/dark mode */
.markdown-body {
  color: rgb(31 41 55);
}

.dark .markdown-body {
  color: rgb(255 255 255);
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  color: rgb(17 24 39);
}

.dark .markdown-body h1,
.dark .markdown-body h2,
.dark .markdown-body h3,
.dark .markdown-body h4,
.dark .markdown-body h5,
.dark .markdown-body h6 {
  color: rgb(243 244 246);
}

.markdown-body p {
  color: rgb(55 65 81);
}

.dark .markdown-body p {
  color: rgb(209 213 219);
}

.markdown-body code {
  background-color: rgb(243 244 246);
  color: rgb(31 41 55);
}

.dark .markdown-body code {
  background-color: rgb(31 41 55);
  color: rgb(229 231 235);
}

.markdown-body pre {
  background-color: rgb(243 244 246);
  border: 1px solid rgb(229 231 235);
}

.dark .markdown-body pre {
  background-color: rgb(31 41 55);
  border: 1px solid rgb(55 65 81);
}

.markdown-body blockquote {
  border-left: 4px solid rgb(209 213 219);
  background-color: rgb(249 250 251);
}

.dark .markdown-body blockquote {
  border-left: 4px solid rgb(75 85 99);
  background-color: rgb(31 41 55);
}

.markdown-body table {
  border: 1px solid rgb(229 231 235);
}

.dark .markdown-body table {
  border: 1px solid rgb(55 65 81);
}

.markdown-body th,
.markdown-body td {
  border: 1px solid rgb(229 231 235);
}

.dark .markdown-body th,
.dark .markdown-body td {
  border: 1px solid rgb(55 65 81);
}

.sidebar-container {
  background: rgba(255, 255, 255, 0.6);
  border-right: 1px solid #e5e7eb;
  backdrop-filter: blur(24px);
}
.dark .sidebar-container {
  background: rgba(16, 24, 28, 0.8);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(24px);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
