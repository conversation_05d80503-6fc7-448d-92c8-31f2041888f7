"use client";
import { GraphsRow } from "@/components/graphs";
import FigmaTableDemo from "@/components/table";
import { Dashboard } from "@/components/dashboard";
import { But<PERSON> } from "@/components/ui/button";
import { OpeningAnimation } from "@/components/ui/opening-animation";
import { useEffect, useState } from "react";
import { collectionData, sampleChartData } from "./dummy-data/information";

// Import custom hooks
import { useDatabaseOperations, useTour, useUserSettings } from "@/lib/hooks";

export default function DashboardPage() {
  const [showOpeningAnimation, setShowOpeningAnimation] = useState(false);

  const databaseOps = useDatabaseOperations();
  const tour = useTour();
  const userSettings = useUserSettings();

  // Initialize component
  useEffect(() => {
    // Fetch query history with default user ID on first load
    databaseOps.fetchQueryHistory(userSettings.userId);

    const hasSeen =
      typeof window !== "undefined" &&
      localStorage.getItem("welcome-animation-shown");
    if (!hasSeen) {
      setShowOpeningAnimation(true);
    }
  }, []);

  // Fetch history when userId changes
  useEffect(() => {
    if (userSettings.userId) {
      databaseOps.fetchQueryHistory(userSettings.userId);
    }
  }, [userSettings.userId]);

  // toggleTheme is already available from the useTheme hook

  const handleOpeningComplete = () => {
    setShowOpeningAnimation(false);
    if (typeof window !== "undefined") {
      localStorage.setItem("welcome-animation-shown", "true");
    }
    // Use tour hook to handle tour logic
    tour.handleOpeningComplete();
  };

  // Remove Sidebar, Navbar, and SPA-style conditional rendering
  // Only render the Dashboard component and related UI
  return (
    <>
      {showOpeningAnimation ? (
        <OpeningAnimation duration={4000} onComplete={handleOpeningComplete}>
          <div />
        </OpeningAnimation>
      ) : (
        <main className="flex-1 flex flex-col p-5 gap-6 animate-[fadeIn_0.5s_ease-out_forwards] ml-3">
          <Dashboard
            data={collectionData["dashboard"]}
            onNavigate={(key) => {
              if (key === "db") window.location.href = "/db-knowledge";
              else if (key === "File system")
                window.location.href = "/file-system";
              else if (key === "HR Knowledge")
                window.location.href = "/hr-knowledge";
              else if (key === "Support Team")
                window.location.href = "/support-team";
              else window.location.href = "/";
            }}
          />
        </main>
      )}
    </>
  );
}
